from app.ai.tools import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>P<PERSON><PERSON>, Tool, Too<PERSON><PERSON><PERSON><PERSON>

# from app.core.issues import IssueRepository
from app.core.actionitems import ActionItemRepository
from app.core.ai.payments_tools import schedule_one_off_communication_tool

# get_issue_tool = Tool(
#     "get_issue",
#     "Get the issue (including 'defaulter_id') by email address. Needs to be called before tools which require defaulter_id.",
#     IssueRepository.get_issue,
#     ToolParams(
#         [
#             ToolParam(
#                 "email_address",
#                 "string",
#                 "The defaulter's email address.",
#                 required=True,
#             )
#         ]
#     ),
# )

# get_comm_history_tool = Tool(
#     "get_comm_history",
#     "Get the communication history for the issue.",
#     IssueRepository.get_comm_history,
#     ToolParams(
#         [
#             ToolParam(
#                 "defaulter_id",
#                 "string",
#                 "The issue id associated with the outstanding debt.",
#                 required=True,
#             )
#         ]
#     ),
# )


# get_debt_info_tool = Tool(
#     "get_debt_info",
#     "Get the debt info including id, name, email, phone, outstanding_amount, delinquency_date, description, creditor_name, and defaulter_id",
#     IssueRepository.get_debt_info,
#     ToolParams(
#         [
#             ToolParam(
#                 "defaulter_id",
#                 "string",
#                 "The issue id associated with the outstanding debt.",
#                 required=True,
#             )
#         ]
#     ),
# )

# get_summary_tool = Tool(
#     "get_summary",
#     "Get the most recent summary for the issue.",
#     IssueRepository.get_summary,
#     ToolParams(
#         [
#             ToolParam(
#                 "defaulter_id",
#                 "string",
#                 "The issue id associated with the outstanding debt.",
#                 required=True,
#             )
#         ]
#     ),
# )


create_action_item_tool = Tool(
    "create_action_item",
    "Schedule an action item for the user.",
    ActionItemRepository.create,
    ToolParams(
        [
            ToolParam(
                "action_date",
                "string",
                "The date the action item is scheduled for.",
                required=True,
            ),
            ToolParam(
                "action_time",
                "string",
                "The time the action item is scheduled for.",
                required=True,
            ),
            ToolParam(
                "action_channel",
                "string",
                "The channel the action item will be executed on. Must be one of the available channels from the organization's preferences.",
                required=True,
            ),
            ToolParam(
                "defaulter_id",
                "string",
                "The issue id associated with the outstanding debt.",
                required=True,
            ),
            ToolParam(
                "action_reason",
                "string",
                "Describe the reasoning and purpose of the action item in detail based on the most recent communication with the defaulter in line with the organization's strategy. Include ample annotations to help the agent understand when and why the action item was created and when it should become irrelevant and deletable.",
                required=True,
            ),
            ToolParam(
                "payment_likelihood",
                "string",
                "The likelihood of payment for the defaulter on a scale from 0 to 5.",
                required=True,
            ),
            ToolParam(
                "category",
                "string",
                "The category of the action item. The category should be 'outreach'.",
                required=True,
            ),
        ]
    ),
)

get_all_action_items_for_issue_tool = Tool(
    "get_all_action_items_for_issue",
    "Get all action items for the issue.",
    ActionItemRepository.get_all_for_issue,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The issue id associated with the outstanding debt.",
                required=True,
            )
        ]
    ),
)

delete_action_item_tool = Tool(
    "delete_action_item",
    "Delete the action item by id.",
    ActionItemRepository.delete,
    ToolParams(
        [
            ToolParam(
                "id",
                "string",
                "The id of the action item.",
                required=True,
            )
        ]
    ),
)

update_action_item_tool = Tool(
    "update_action_item",
    "Update a single action item by id.",
    ActionItemRepository.update,
    ToolParams(
        [
            ToolParam(
                "action_item_id",
                "string",
                "The id of the action item. Make sure this comes from the field, 'id' associated with the specific action item returned by the get_all_action_items_for_issue. Do not use any other id!",
                required=True,
            ),
            ToolParam(
                "action_date",
                "string",
                "The date the action item is scheduled for.",
                required=False,
            ),
            ToolParam(
                "action_time",
                "string",
                "The time the action item is scheduled for.",
                required=False,
            ),
            ToolParam(
                "action_channel",
                "string",
                "The channel the action item will be executed on. Should be one of the available channels from the organization's preferences.",
                required=False,
            ),
            ToolParam(
                "action_reason",
                "string",
                "Describe the reasoning and purpose of the action item in detail based on the most recent communication with the defaulter in line with the organization's strategy. Include ample annotations to help the agent understand when and why the action item was created and when it should become irrelevant and deletable.",
                required=False,
            ),
        ]
    ),
)

# predict_payment_likelihood_tool = Tool(
#     "predict_payment_likelihood",
#     "Predict the likelihood of payment for the defaulter.",
#     ActionItemRepository.predict_payment_likelihood,
#     ToolParams(
#         [
#             ToolParam(
#                 "conversation",
#                 "string",
#                 "The transcript of the most recent conversation with the defaulter.",
#                 required=True,
#             ),
#         ]
#     ),
# )


# def make_tool_engine():
#     engine = ToolEngine(IssueRepository)
#     engine.register(get_issue_tool)
#     engine.register(get_comm_history_tool)
#     engine.register(get_debt_info_tool)
#     engine.register(get_summary_tool)
#     return engine


# tool_engine = make_tool_engine()


class NoClass:
    def __init__(self):
        pass

    def no_op(self):
        return "Done"


no_op_tool = Tool("no_op", "Do nothing", NoClass.no_op, [])


def make_no_tools_engine():
    engine = ToolEngine(NoClass)
    engine.register(no_op_tool)
    return engine


def make_analyzer_tool_engine():
   
    engine = ToolEngine(ActionItemRepository)
    engine.register(create_action_item_tool)
    engine.register(get_all_action_items_for_issue_tool)
    engine.register(delete_action_item_tool)
    engine.register(update_action_item_tool)
    engine.register(schedule_one_off_communication_tool)
    # engine.register(predict_payment_likelihood_tool)
    return engine


analyzer_tool_engine = make_analyzer_tool_engine()
no_tools_engine = make_no_tools_engine()
